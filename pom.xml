<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yxt.order.assistant</groupId>
    <artifactId>order-assistant</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>order-assistant</name>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.yxt</groupId>
        <artifactId>yxt-xframe2</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <properties>
        <java.version>21</java.version>
        <snakeyaml.version>2.2</snakeyaml.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <module.deploy.skip>false</module.deploy.skip>
        <spring.cloud.alibaba.version>2023.0.3.2</spring.cloud.alibaba.version>
        <alibaba.sentinel.nacos.version>1.8.8</alibaba.sentinel.nacos.version>
        <spring.cloud.version>2025.0.0</spring.cloud.version>

        <!-- 适配jenkins的maven版本 -->
        <maven-clean-plugin.version>3.3.2</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    </properties>

    <modules>
        <module>order-assistant-query</module>
        <module>order-assistant-operate</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>yxt-common-lang2</artifactId>
                <version>${yxt-common.version}</version>
            </dependency>

            <!--统一管理spring ai相关依赖的版本-->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>2.0.12</version>
            </dependency>
            <dependency>
                <groupId>io.reactivex.rxjava2</groupId>
                <artifactId>rxjava</artifactId>
                <version>2.2.21</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor.addons</groupId>
                <artifactId>reactor-adapter</artifactId>
                <version>3.5.2</version>
            </dependency>
            <dependency>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <groupId>com.alibaba.cloud</groupId>
                <version>${spring.cloud.alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-nacos</artifactId>
                <version>${alibaba.sentinel.nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>io.github.imfangs</groupId>
                <artifactId>dify-java-client</artifactId>
                <version>1.1.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <useIncrementalCompilation>false</useIncrementalCompilation>
                        <fork>true</fork>
                        <compilerArgs>
                            <arg>-Xlint:unchecked</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
