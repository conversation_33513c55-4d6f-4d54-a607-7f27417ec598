<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-xframe2</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.yxt.order.assistant</groupId>
  <artifactId>order-assistant</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>order-assistant</name>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>order-assistant-query</module>
    <module>order-assistant-operate</module>
  </modules>
  <properties>
    <spring-ai.version>1.0.0</spring-ai.version>
    <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
    <spring.cloud.version>2025.0.0</spring.cloud.version>
    <spring.cloud.alibaba.version>2023.0.3.2</spring.cloud.alibaba.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
    <snakeyaml.version>2.2</snakeyaml.version>
    <module.deploy.skip>false</module.deploy.skip>
    <java.version>21</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <alibaba.sentinel.nacos.version>1.8.8</alibaba.sentinel.nacos.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <maven-clean-plugin.version>3.3.2</maven-clean-plugin.version>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>${spring.cloud.alibaba.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        <version>${spring.cloud.alibaba.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring.cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.yxt</groupId>
        <artifactId>yxt-common-lang2</artifactId>
        <version>${yxt-common.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-bom</artifactId>
        <version>${spring-ai.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>2.0.12</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex.rxjava2</groupId>
        <artifactId>rxjava</artifactId>
        <version>2.2.21</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-adapter</artifactId>
        <version>3.5.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        <version>${spring.cloud.alibaba.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-nacos</artifactId>
        <version>${alibaba.sentinel.nacos.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.validation</groupId>
        <artifactId>validation-api</artifactId>
        <version>2.0.1.Final</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>io.github.imfangs</groupId>
        <artifactId>dify-java-client</artifactId>
        <version>1.1.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
