server:
  port: 8082
spring:
  ai:
    mcp:
      server:
        name: order-assistant-query
        version: 1.0.0
        type: ASYNC  # Recommended for reactive applications
        sse-message-endpoint: /mcp/message
        sse-endpoint: /sse
    alibaba:
      mcp:
        nacos:
          enabled: true
          server-addr: 10.4.3.219:8848
          username: nacos
          password: nacos
          registry:
            enabled: true
            service-namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
            service-group: MCP_SERVER_GROUP_DEV
#          dynamic:
#            service-namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
#            service-group: DEFAULT_GROUP
#            service-names: # 其他注册MCP服务的服务名称
#              - yxt-devops-mcp-server-mcp-service


management:
  health:
    defaults:
      enabled: false # 默认健康检测不进行所有的依赖项健康检测
  endpoints: # 配置暴露和屏蔽的端点
    web:
      exposure:
        include: ["*"] # 暴露所有端点
  endpoint:
    health:
      show-details: "ALWAYS" # 显示所有健康检查信息
logging:
  level:
    org.springframework.web: DEBUG
    org.springframework.web.reactive: DEBUG
    org.springframework.web.servlet: DEBUG